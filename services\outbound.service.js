const fs = require('fs');
const path = require('path');
const { S3 } = require('@aws-sdk/client-s3');
const SftpClient = require('ssh2-sftp-client');
const { BlobServiceClient } = require('@azure/storage-blob');
const axios = require('axios');
const logger = require('../config/logger');

/**
 * Upload CSV file to various destinations based on agent configuration
 * @param {string} csvFilePath - Path to the CSV file to upload
 * @param {Object} agent - Agent configuration object
 * @param {Object} performanceMonitor - Performance monitoring instance
 * @returns {Promise<Object>} Upload result with status and details
 */
async function uploadCSVFile(csvFilePath, agent, performanceMonitor) {
    const uploadResults = {
        success: false,
        destination: agent.source,
        originalPath: csvFilePath,
        uploadedPath: null,
        error: null,
        fileSize: 0
    };

    try {
        // Get file stats
        const stats = fs.statSync(csvFilePath);
        uploadResults.fileSize = stats.size;

        const settings = agent.settingsObj;
        const fileName = path.basename(csvFilePath);

        performanceMonitor?.startStep(`Upload to ${agent.source}`, {
            agentName: agent.name,
            destination: agent.source,
            fileName,
            fileSize: uploadResults.fileSize
        });

        switch (agent.source) {
            case 'Local':
                uploadResults.uploadedPath = await uploadToLocal(csvFilePath, settings, fileName);
                break;
            case 'AWSS3':
                uploadResults.uploadedPath = await uploadToS3(csvFilePath, settings, fileName);
                break;
            case 'AzureBlob':
                uploadResults.uploadedPath = await uploadToAzureBlob(csvFilePath, settings, fileName);
                break;
            case 'FileTransfer':
                uploadResults.uploadedPath = await uploadToSFTP(csvFilePath, settings, fileName);
                break;
            case 'URL':
                uploadResults.uploadedPath = await uploadToHTTP(csvFilePath, settings, fileName);
                break;
            default:
                throw new Error(`Unsupported destination type: ${agent.source}`);
        }

        uploadResults.success = true;
        logger.info(`Successfully uploaded CSV to ${agent.source}: ${uploadResults.uploadedPath}`);

        performanceMonitor?.endStep(`Upload to ${agent.source}`, {
            status: 'success',
            uploadedPath: uploadResults.uploadedPath,
            fileSize: uploadResults.fileSize
        });

    } catch (error) {
        uploadResults.error = error.message;
        logger.error(`Error uploading CSV to ${agent.source}:`, error.message);

        performanceMonitor?.endStep(`Upload to ${agent.source}`, {
            status: 'error',
            error: error.message
        });
    }

    return uploadResults;
}

/**
 * Upload file to local directory
 */
async function uploadToLocal(csvFilePath, settings, fileName) {
    const outputDir = settings.directory_path;
    if (!outputDir) {
        throw new Error('No directory_path setting found for Local destination');
    }

    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        logger.info(`Created output directory: ${outputDir}`);
    }

    const destinationPath = path.join(outputDir, fileName);
    
    // Handle file name conflicts
    let finalDestinationPath = destinationPath;
    let counter = 1;
    while (fs.existsSync(finalDestinationPath)) {
        const ext = path.extname(fileName);
        const name = path.basename(fileName, ext);
        finalDestinationPath = path.join(outputDir, `${name}_${counter}${ext}`);
        counter++;
    }

    // Copy file to destination
    fs.copyFileSync(csvFilePath, finalDestinationPath);
    logger.info(`Copied CSV file to local directory: ${finalDestinationPath}`);

    return finalDestinationPath;
}

/**
 * Upload file to AWS S3
 */
async function uploadToS3(csvFilePath, settings, fileName) {
    const accessKeyId = settings.access_key_id;
    const secretAccessKey = settings.secret_access_key;
    const bucketName = settings.bucket_name;
    const region = settings.region || 'us-east-1';
    const s3Path = settings.s3_path || '';

    if (!accessKeyId || !secretAccessKey || !bucketName) {
        throw new Error('Missing AWS S3 credentials (access_key_id, secret_access_key, bucket_name)');
    }

    const s3 = new S3({ region, credentials: { accessKeyId, secretAccessKey } });
    
    // Construct S3 key
    const s3Key = s3Path ? `${s3Path}/${fileName}` : fileName;
    
    // Handle key conflicts
    let finalS3Key = s3Key;
    let counter = 1;
    while (true) {
        try {
            await s3.headObject({ Bucket: bucketName, Key: finalS3Key });
            // File exists, try with counter
            const ext = path.extname(fileName);
            const name = path.basename(fileName, ext);
            const basePath = s3Path ? `${s3Path}/${name}` : name;
            finalS3Key = `${basePath}_${counter}${ext}`;
            counter++;
        } catch (error) {
            if (error.name === 'NotFound') {
                // File doesn't exist, we can use this key
                break;
            }
            throw error; // Re-throw other errors
        }
    }

    // Read file and upload
    const fileContent = fs.readFileSync(csvFilePath);
    await s3.putObject({
        Bucket: bucketName,
        Key: finalS3Key,
        Body: fileContent,
        ContentType: 'text/csv'
    });

    const s3Url = `s3://${bucketName}/${finalS3Key}`;
    logger.info(`Uploaded CSV file to S3: ${s3Url}`);
    return s3Url;
}

/**
 * Upload file to Azure Blob Storage
 */
async function uploadToAzureBlob(csvFilePath, settings, fileName) {
    const connectionString = settings.connection_string;
    const containerName = settings.container_name;
    const blobPath = settings.blob_path || '';

    if (!connectionString || !containerName) {
        throw new Error('Missing Azure Blob credentials (connection_string, container_name)');
    }

    const client = BlobServiceClient.fromConnectionString(connectionString);
    const container = client.getContainerClient(containerName);

    // Construct blob name
    const blobName = blobPath ? `${blobPath}/${fileName}` : fileName;
    
    // Handle blob name conflicts
    let finalBlobName = blobName;
    let counter = 1;
    while (true) {
        try {
            const blobClient = container.getBlobClient(finalBlobName);
            const exists = await blobClient.exists();
            if (!exists) {
                break; // Blob doesn't exist, we can use this name
            }
            // Blob exists, try with counter
            const ext = path.extname(fileName);
            const name = path.basename(fileName, ext);
            const basePath = blobPath ? `${blobPath}/${name}` : name;
            finalBlobName = `${basePath}_${counter}${ext}`;
            counter++;
        } catch (error) {
            logger.error(`Error checking Azure blob existence: ${error.message}`);
            break;
        }
    }

    // Read file and upload
    const fileContent = fs.readFileSync(csvFilePath);
    const blobClient = container.getBlobClient(finalBlobName);
    await blobClient.upload(fileContent, fileContent.length, {
        blobHTTPHeaders: { blobContentType: 'text/csv' }
    });

    const blobUrl = `https://${client.accountName}.blob.core.windows.net/${containerName}/${finalBlobName}`;
    logger.info(`Uploaded CSV file to Azure Blob: ${blobUrl}`);
    return blobUrl;
}

/**
 * Upload file to SFTP server
 */
async function uploadToSFTP(csvFilePath, settings, fileName) {
    const host = settings.host;
    const port = parseInt(settings.port) || 22;
    const username = settings.username;
    const password = settings.password;
    const remotePath = settings.path || '/';

    if (!host || !username || !password) {
        throw new Error('Missing SFTP credentials (host, username, password)');
    }

    const client = new SftpClient();
    try {
        await client.connect({ host, port, username, password });

        // Ensure remote directory exists
        const remoteDir = path.posix.dirname(remotePath);
        if (remoteDir !== '.' && remoteDir !== '/') {
            try {
                const dirExists = await client.exists(remoteDir);
                if (!dirExists) {
                    await client.mkdir(remoteDir, true);
                    logger.info(`Created remote directory: ${remoteDir}`);
                }
            } catch (error) {
                logger.warn(`Could not create remote directory ${remoteDir}: ${error.message}`);
            }
        }

        // Construct remote file path
        const remoteFilePath = path.posix.join(remotePath, fileName);
        
        // Handle file name conflicts
        let finalRemoteFilePath = remoteFilePath;
        let counter = 1;
        while (await client.exists(finalRemoteFilePath)) {
            const ext = path.extname(fileName);
            const name = path.basename(fileName, ext);
            finalRemoteFilePath = path.posix.join(remotePath, `${name}_${counter}${ext}`);
            counter++;
        }

        // Upload file
        await client.fastPut(csvFilePath, finalRemoteFilePath);
        logger.info(`Uploaded CSV file to SFTP: ${finalRemoteFilePath}`);
        return finalRemoteFilePath;

    } finally {
        await client.end();
    }
}

/**
 * Upload file via HTTP POST
 */
async function uploadToHTTP(csvFilePath, settings, fileName) {
    const url = settings.url;
    const method = settings.method || 'POST';
    const headers = settings.headers ? JSON.parse(settings.headers) : {};

    if (!url) {
        throw new Error('No URL setting found for HTTP destination');
    }

    // Read file content
    const fileContent = fs.readFileSync(csvFilePath);
    
    // Prepare form data or direct upload based on configuration
    const uploadMethod = settings.upload_method || 'form'; // 'form' or 'direct'
    
    let requestConfig = {
        method,
        url,
        headers: {
            'Content-Type': uploadMethod === 'form' ? 'multipart/form-data' : 'text/csv',
            ...headers
        }
    };

    if (uploadMethod === 'form') {
        // Use form data
        const FormData = require('form-data');
        const form = new FormData();
        form.append('file', fileContent, {
            filename: fileName,
            contentType: 'text/csv'
        });
        requestConfig.data = form;
        requestConfig.headers = {
            ...requestConfig.headers,
            ...form.getHeaders()
        };
    } else {
        // Direct upload
        requestConfig.data = fileContent;
    }

    const response = await axios(requestConfig);
    
    if (response.status >= 200 && response.status < 300) {
        logger.info(`Successfully uploaded CSV file via HTTP to: ${url}`);
        return `${url} (HTTP ${response.status})`;
    } else {
        throw new Error(`HTTP upload failed with status ${response.status}: ${response.statusText}`);
    }
}

module.exports = {
    uploadCSVFile
};
